import { composeResolvers } from '@graphql-tools/resolvers-composition';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';
import InSamePartner from '../../../resolvers/composition/InSamePartner.js';
import { logger } from '../../../utils/logger.js';
import type {
  GqlCreateTagAutomationInput as CreateTagAutomationInput,
  GqlUpdateTagAutomationInput as UpdateTagAutomationInput,
  GqlDeleteTagAutomationInput as DeleteTagAutomationInput,
  GqlResolvers as Resolvers,
} from '../../../graphql/__generated__/resolvers-types.js';

const tagAutomationResolvers: Resolvers = {
  Query: {
    tagAutomations: async (_root, _, context) => {
      return context.services.tagAutomations.findByPartnerId(context.token.partnerId);
    },
  },
  Mutation: {
    tagAutomations: () => ({ _type: 'TagAutomationsMutations' }),
  },
  TagAutomation: {
    partner: async (tagAutomation: any, _: any, context: any) => {
      return (
        tagAutomation.partner ??
        (tagAutomation.partnerId
          ? context.services.partners.findById(tagAutomation.partnerId)
          : null)
      );
    },
    program: async (tagAutomation: any, _: any, context: any) => {
      return (
        tagAutomation.program ??
        (tagAutomation.programId
          ? context.services.programs.findById(tagAutomation.programId)
          : null)
      );
    },
    tags: async (tagAutomation: any) => {
      // Tags are eagerly loaded in the entity, so they should already be available
      return tagAutomation.tags ?? [];
    },
  },
  TagAutomationsMutations: {
    create: async (_: any, { input }: any, context: any) => {
      logger.info('tagAutomationResolvers.TagAutomationsMutations.create');
      return context.services.tagAutomations.create(context.token, input);
    },
    update: async (_: any, { input }: any, context: any) => {
      logger.info('tagAutomationResolvers.TagAutomationsMutations.update');
      return context.services.tagAutomations.update(context.token, input);
    },
    delete: async (_: any, { input }: any, context: any) => {
      logger.info('tagAutomationResolvers.TagAutomationsMutations.delete');
      return context.services.tagAutomations.delete(context.token, input);
    },
  },
};

const resolverComposition = {
  'Query.tagAutomations': [IsAdmin()],
  'TagAutomation.partner': [IsAdmin()],
  'TagAutomation.program': [IsAdmin()],
  'TagAutomation.tags': [IsAdmin()],
  'TagAutomationsMutations.create': [
    IsAdmin(),
    InSamePartner<{ input: CreateTagAutomationInput }>(
      async (_, context) => context.token.partnerId,
    ),
  ],
  'TagAutomationsMutations.update': [
    IsAdmin(),
    InSamePartner<{ input: UpdateTagAutomationInput }>(async ({ input }, context) => {
      const automation = await context.services.tagAutomations.findById(input.id);
      return automation?.partnerId ?? null;
    }),
  ],
  'TagAutomationsMutations.delete': [
    IsAdmin(),
    InSamePartner<{ input: DeleteTagAutomationInput }>(async ({ input }, context) => {
      const automation = await context.services.tagAutomations.findById(input.id);
      return automation?.partnerId ?? null;
    }),
  ],
};

export default composeResolvers(tagAutomationResolvers, resolverComposition);
